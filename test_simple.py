#!/usr/bin/env python3
"""
简单测试脚本
"""

import sys
import os
from pathlib import Path

print("🚀 简单测试开始")
print(f"Python版本: {sys.version}")
print(f"当前目录: {os.getcwd()}")
print(f"项目根目录: {Path(__file__).parent}")

try:
    from config import config
    print("✅ 配置模块导入成功")
    print(f"Google API配置: {config.google_search_configured}")
    print(f"Reddit API配置: {config.reddit_configured}")
except Exception as e:
    print(f"❌ 配置模块导入失败: {e}")

try:
    from services.cogbridges_service import CogBridgesService
    print("✅ CogBridges服务导入成功")
except Exception as e:
    print(f"❌ CogBridges服务导入失败: {e}")

print("🎉 简单测试完成")
