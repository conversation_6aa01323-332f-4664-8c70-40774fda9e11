2025-07-19 15:26:52 - services.google_search_api - ERROR - Google Custom Search API未配置
2025-07-19 15:26:52 - __main__ - ERROR - 集成测试失败: Google Custom Search API配置不完整
2025-07-19 15:36:10 - __main__ - ERROR - 集成测试失败: 'Config' object has no attribute 'GOOGLE_SEARCH_API_KEY'
2025-07-19 15:41:25 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:41:25 - utils.proxy_utils - INFO - 未配置代理
2025-07-19 15:41:25 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:41:25 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:41:25 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:41:25 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, or <PERSON>?
2025-07-19 15:41:25 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:41:25 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:41:25 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:41:25 - utils.proxy_utils - INFO - 未配置代理
2025-07-19 15:41:28 - services.google_search_api - ERROR - Google搜索失败: 400 Client Error: Bad Request for url: https://www.googleapis.com/customsearch/v1?key=AIzaSyDaHYlQI-WbqVxPb5U4R8X9J2K3L4M5N6O7&cx=017576662512468239146%3Aomuauf_lfve&q=Should+I+subscribe+to+GPT%2C+Claude%2C+Grok%2C+or+Gemini%3F+site%3Areddit.com&num=5&safe=off&fields=items%28title%2Clink%2Csnippet%2CdisplayLink%29%2CsearchInformation%28totalResults%29
2025-07-19 15:41:28 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 3.06秒
2025-07-19 15:41:28 - services.cogbridges_service - ERROR - CogBridges搜索失败: Google搜索失败: 400 Client Error: Bad Request for url: https://www.googleapis.com/customsearch/v1?key=AIzaSyDaHYlQI-WbqVxPb5U4R8X9J2K3L4M5N6O7&cx=017576662512468239146%3Aomuauf_lfve&q=Should+I+subscribe+to+GPT%2C+Claude%2C+Grok%2C+or+Gemini%3F+site%3Areddit.com&num=5&safe=off&fields=items%28title%2Clink%2Csnippet%2CdisplayLink%29%2CsearchInformation%28totalResults%29
2025-07-19 15:48:16 - __main__ - ERROR - 集成测试失败: 'Config' object has no attribute 'GOOGLE_SEARCH_API_KEY'
2025-07-19 15:48:57 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:48:57 - utils.proxy_utils - INFO - 未配置代理
2025-07-19 15:48:57 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:48:57 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:48:57 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:48:57 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:48:57 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:48:57 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:48:57 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:48:57 - utils.proxy_utils - INFO - 未配置代理
2025-07-19 15:48:59 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.34秒
2025-07-19 15:48:59 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.34秒
2025-07-19 15:48:59 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.34秒
2025-07-19 15:51:24 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:51:26 - utils.proxy_utils - INFO - 代理连接测试成功
2025-07-19 15:51:26 - utils.proxy_utils - INFO - 当前IP: ************
2025-07-19 15:51:26 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:51:26 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:51:26 - services.google_search_api - INFO - 开始Google Custom Search API搜索: lectures
2025-07-19 15:51:26 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:51:31 - services.google_search_api - ERROR - Google搜索失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-19 15:51:31 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 5.02秒
2025-07-19 15:52:26 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:52:27 - utils.proxy_utils - INFO - 代理连接测试成功
2025-07-19 15:52:27 - utils.proxy_utils - INFO - 当前IP: ************
2025-07-19 15:52:27 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:52:27 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:52:27 - services.google_search_api - INFO - 开始Google Custom Search API搜索: lectures
2025-07-19 15:52:27 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:52:29 - services.google_search_api - INFO - Google搜索完成: 找到 1 个结果（总计 630000000），耗时 2.09秒
2025-07-19 15:52:29 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 2.09秒
2025-07-19 15:52:29 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:52:29 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:52:29 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:52:29 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:52:31 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.70秒
2025-07-19 15:52:31 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.70秒
2025-07-19 15:52:54 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:52:54 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:52:56 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:52:56 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:52:56 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:52:56 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:52:56 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:52:56 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:52:56 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:52:56 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:53:01 - services.google_search_api - ERROR - Google搜索失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-19 15:53:01 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 5.03秒
2025-07-19 15:53:01 - services.cogbridges_service - ERROR - CogBridges搜索失败: Google搜索失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-19 15:53:34 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:53:34 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:53:34 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:53:34 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:53:34 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:53:34 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:53:34 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:53:34 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:53:34 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:53:34 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:53:36 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.66秒
2025-07-19 15:53:36 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.66秒
2025-07-19 15:53:36 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.67秒
2025-07-19 15:55:00 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:55:00 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:55:00 - services.google_search_api - INFO - 开始Google Custom Search API搜索: lectures
2025-07-19 15:55:00 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:55:01 - services.google_search_api - INFO - Google搜索完成: 找到 3 个结果（总计 630000000），耗时 1.20秒
2025-07-19 15:55:01 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.20秒
2025-07-19 15:55:01 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:55:01 - services.google_search_api - INFO - 开始Google Custom Search API搜索: reddit GPT Claude
2025-07-19 15:55:01 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:55:02 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 0.99秒
2025-07-19 15:55:02 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.99秒
2025-07-19 15:55:02 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:55:02 - services.google_search_api - INFO - 开始Google Custom Search API搜索: GPT Claude
2025-07-19 15:55:02 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:55:03 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.09秒
2025-07-19 15:55:03 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.09秒
2025-07-19 15:55:28 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:55:28 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:55:28 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:55:28 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:55:28 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:55:28 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:55:28 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:55:28 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:55:28 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? reddit
2025-07-19 15:55:28 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:55:32 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 3.43秒
2025-07-19 15:55:32 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 3.43秒
2025-07-19 15:55:32 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 3.44秒
2025-07-19 15:56:06 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:29 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:29 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:58:29 - services.reddit_service - INFO - Reddit API连接测试成功
2025-07-19 15:58:29 - api.Reddit - INFO - 开始调用 Reddit API: GET user
2025-07-19 15:58:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 15:58:29 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:41 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 15:58:41 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:41 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 15:58:41 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 15:58:41 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 15:58:41 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:58:41 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:58:41 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:58:41 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? site:reddit.com
2025-07-19 15:58:41 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:43 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.62秒
2025-07-19 15:58:43 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.62秒
2025-07-19 15:58:43 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:58:43 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? reddit
2025-07-19 15:58:43 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:44 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.16秒
2025-07-19 15:58:44 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.16秒
2025-07-19 15:58:44 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:58:44 - services.google_search_api - INFO - 开始Google Custom Search API搜索: reddit Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 15:58:44 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:45 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.55秒
2025-07-19 15:58:45 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.55秒
2025-07-19 15:58:45 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 15:58:45 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? reddit.com
2025-07-19 15:58:45 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:47 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.58秒
2025-07-19 15:58:47 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.58秒
2025-07-19 15:58:47 - services.cogbridges_service - INFO - Google搜索Reddit结果不足，使用备用Reddit URL
2025-07-19 15:58:47 - services.cogbridges_service - INFO - 步骤1完成: 找到 2 个Reddit帖子, 耗时: 5.93秒
2025-07-19 15:58:47 - services.cogbridges_service - INFO - 步骤2: 并行获取 2 个Reddit帖子的内容和评论
2025-07-19 15:58:47 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 15:58:47 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 15:58:47 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 15:58:47 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 15:58:47 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 15:58:49 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-19 15:58:49 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 404 HTTP response
2025-07-19 15:58:49 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 0 个帖子数据, 耗时: 2.51秒
2025-07-19 16:08:21 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:08:21 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:08:21 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:08:21 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:08:21 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:08:21 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:08:21 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:08:21 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 16:08:21 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? site:reddit.com
2025-07-19 16:08:21 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:08:23 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.25秒
2025-07-19 16:08:23 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.25秒
2025-07-19 16:08:23 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.26秒
2025-07-19 16:09:25 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:09:25 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:09:25 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:09:25 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:09:25 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:09:25 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:09:25 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:09:25 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 16:09:25 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? site: reddit
2025-07-19 16:09:25 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:09:27 - services.google_search_api - INFO - Google搜索完成: 找到 0 个结果（总计 0），耗时 1.71秒
2025-07-19 16:09:27 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.71秒
2025-07-19 16:09:27 - services.cogbridges_service - INFO - 步骤1完成: 找到 0 个Reddit帖子, 耗时: 1.72秒
2025-07-19 16:11:37 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:11:37 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:11:37 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:11:37 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:11:37 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:11:37 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:11:37 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:11:37 - services.cogbridges_service - INFO - 步骤1完成: 找到 3 个Reddit帖子, 耗时: 0.00秒
2025-07-19 16:11:37 - services.cogbridges_service - INFO - 步骤2: 并行获取 3 个Reddit帖子的内容和评论
2025-07-19 16:11:37 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:11:37 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:11:37 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:11:37 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:11:37 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:11:37 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:11:37 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:11:39 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 403 HTTP response
2025-07-19 16:11:40 - services.reddit_service - INFO - 获取帖子成功: 🌸 NEW YesStyle CODE! %20 off entire purchase Coupo...
2025-07-19 16:11:40 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:11:40 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:11:40 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 403 HTTP response
2025-07-19 16:11:41 - services.reddit_service - INFO - 获取评论成功: 0 条评论
2025-07-19 16:11:41 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 1 个帖子数据, 耗时: 3.82秒
2025-07-19 16:11:41 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 16:11:41 - services.cogbridges_service - INFO - 需要获取 0 个评论者的历史数据
2025-07-19 16:11:41 - services.cogbridges_service - INFO - 步骤3完成: 获取了 0 个用户的历史数据, 耗时: 0.00秒
2025-07-19 16:11:41 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_161137_e2035bba.json
2025-07-19 16:11:41 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 3.82秒
2025-07-19 16:14:00 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:14:00 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:14:00 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:14:00 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:14:00 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:14:03 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 403 HTTP response
2025-07-19 16:14:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:14:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:14:03 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 403 HTTP response
2025-07-19 16:14:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:14:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:14:05 - services.reddit_service - INFO - 获取帖子成功: 🌸 NEW YesStyle CODE! %20 off entire purchase Coupo...
2025-07-19 16:14:05 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:14:05 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:14:06 - services.reddit_service - INFO - 获取评论成功: 0 条评论
2025-07-19 16:19:48 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:19:48 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:19:48 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:19:48 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:19:48 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:19:48 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:19:48 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:19:48 - services.cogbridges_service - INFO - 步骤1完成: 找到 3 个Reddit帖子, 耗时: 0.00秒
2025-07-19 16:19:48 - services.cogbridges_service - INFO - 步骤2: 并行获取 3 个Reddit帖子的内容和评论
2025-07-19 16:19:48 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:19:48 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:19:48 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:19:48 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:19:48 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:19:48 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:19:48 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:19:50 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 403 HTTP response
2025-07-19 16:19:50 - services.reddit_service - ERROR - 获取Reddit帖子失败: received 403 HTTP response
2025-07-19 16:19:52 - services.reddit_service - INFO - 获取帖子成功: 🌸 NEW YesStyle CODE! %20 off entire purchase Coupo...
2025-07-19 16:19:52 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:19:52 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:19:54 - services.reddit_service - INFO - 获取评论成功: 0 条评论
2025-07-19 16:19:54 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 1 个帖子数据, 耗时: 6.03秒
2025-07-19 16:19:54 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 16:19:54 - services.cogbridges_service - INFO - 需要获取 0 个评论者的历史数据
2025-07-19 16:19:54 - services.cogbridges_service - INFO - 步骤3完成: 获取了 0 个用户的历史数据, 耗时: 0.00秒
2025-07-19 16:19:54 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_161948_e2035bba.json
2025-07-19 16:19:54 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 6.03秒
2025-07-19 16:25:16 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:25:16 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:25:16 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:25:16 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:25:16 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:25:16 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:25:16 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:25:16 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 16:25:16 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? site:reddit.com
2025-07-19 16:25:16 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:25:46 - services.google_search_api - ERROR - Google搜索失败: HTTPSConnectionPool(host='www.googleapis.com', port=443): Read timed out. (read timeout=30)
2025-07-19 16:25:46 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 30.03秒
2025-07-19 16:25:46 - services.cogbridges_service - ERROR - CogBridges搜索失败: Google搜索失败: HTTPSConnectionPool(host='www.googleapis.com', port=443): Read timed out. (read timeout=30)
2025-07-19 16:41:10 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:41:10 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:41:10 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:41:10 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:41:10 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:41:10 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:41:10 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:41:10 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 16:41:10 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini? site:reddit.com
2025-07-19 16:41:10 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:41:11 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34200），耗时 1.40秒
2025-07-19 16:41:11 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.40秒
2025-07-19 16:41:11 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个Reddit帖子, 耗时: 1.40秒
2025-07-19 16:41:11 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 16:41:11 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:41:11 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:11 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:41:11 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:41:11 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:11 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:41:11 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:11 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:41:11 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:11 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:41:11 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:14 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 16:41:14 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:41:14 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:15 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:41:16 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 16:41:16 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:41:16 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:17 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 16:41:17 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:41:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:17 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 16:41:17 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:41:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:17 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 16:41:17 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:41:17 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:41:19 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:41:20 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:41:20 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:41:21 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:41:21 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 9.55秒
2025-07-19 16:41:21 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 16:41:21 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 16:41:21 - services.reddit_service - ERROR - 获取用户评论失败: received 403 HTTP response
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 16:41:22 - services.reddit_service - ERROR - 获取用户帖子失败: received 403 HTTP response
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 16:41:22 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 16:41:23 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:23 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 16:41:23 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:23 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 16:41:23 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:23 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:23 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:41:24 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:24 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 16:41:25 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:41:26 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:41:26 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 5.30秒
2025-07-19 16:41:26 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_164110_e2035bba.json
2025-07-19 16:41:26 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 16.24秒
2025-07-19 16:49:42 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 16:49:42 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:49:42 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 16:49:42 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 16:49:42 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 16:49:42 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:49:42 - services.cogbridges_service - INFO - 步骤1: Google搜索Reddit内容 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:49:42 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 16:49:42 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 16:49:42 - utils.proxy_utils - INFO - 代理配置已设置: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:49:43 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 1.50秒
2025-07-19 16:49:43 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.50秒
2025-07-19 16:49:43 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个Reddit帖子, 耗时: 1.50秒
2025-07-19 16:49:43 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 16:49:43 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:49:43 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:43 - services.reddit_service - INFO - Reddit服务使用代理: {'http': 'http://127.0.0.1:7890', 'https': 'http://127.0.0.1:7890'}
2025-07-19 16:49:43 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:49:43 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:43 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:49:43 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:43 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:49:43 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:43 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 16:49:43 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:46 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 16:49:46 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:49:46 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:48 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 16:49:48 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:49:48 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:48 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 16:49:48 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:49:48 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:48 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 16:49:48 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:49:48 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:50 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 16:49:50 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 16:49:50 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 16:49:50 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:49:51 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:49:51 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:49:51 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:49:53 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 16:49:53 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 9.50秒
2025-07-19 16:49:53 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 16:49:53 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 16:49:54 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 16:49:55 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:49:55 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 16:49:55 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:55 - services.reddit_service - ERROR - 获取用户评论失败: received 403 HTTP response
2025-07-19 16:49:55 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: received 403 HTTP response
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 16:49:56 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 16:49:57 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:57 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 16:49:57 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:57 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 16:49:58 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 16:49:58 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 4.74秒
2025-07-19 16:49:58 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_164942_e2035bba.json
2025-07-19 16:49:58 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 15.74秒
2025-07-19 17:33:18 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 17:33:18 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 17:33:19 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 17:33:19 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 17:33:19 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 17:33:19 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 17:33:19 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?, 站点: 全网
2025-07-19 17:33:19 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 17:33:19 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 17:33:19 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 17:33:20 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 1.04秒
2025-07-19 17:33:20 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.05秒
2025-07-19 17:33:20 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 1.06秒
2025-07-19 17:33:20 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 17:33:20 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:33:20 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:20 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-19 17:33:21 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:33:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:21 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:33:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:21 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:33:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:21 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:33:21 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:23 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 17:33:23 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:33:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:24 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:33:25 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 17:33:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:33:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:25 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 17:33:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:33:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:25 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 17:33:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:33:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:25 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 17:33:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:33:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:33:27 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:33:27 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:33:28 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:33:29 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:33:29 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.65秒
2025-07-19 17:33:29 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 17:33:29 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 17:33:30 - services.reddit_service - ERROR - 获取用户评论失败: received 403 HTTP response
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 17:33:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 17:33:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 17:33:30 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 17:33:31 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:32 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 17:33:32 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 17:33:32 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:32 - services.reddit_service - ERROR - 获取用户帖子失败: received 403 HTTP response
2025-07-19 17:33:32 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 17:33:32 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:32 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:32 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:33:33 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:33:33 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 3.89秒
2025-07-19 17:33:33 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_173319_e2035bba.json
2025-07-19 17:33:33 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 13.59秒
2025-07-19 17:38:17 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 17:38:17 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 17:38:17 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 17:38:17 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 17:38:17 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 17:38:17 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 17:38:17 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 17:38:17 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 17:38:17 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 17:38:17 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 17:38:18 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 1.14秒
2025-07-19 17:38:18 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.15秒
2025-07-19 17:38:18 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 1.15秒
2025-07-19 17:38:18 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 17:38:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:38:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:18 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-19 17:38:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:38:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:38:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:38:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:18 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 17:38:18 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:20 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 17:38:20 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:38:20 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:22 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:38:23 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 17:38:23 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:38:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:23 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 17:38:23 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:38:23 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:24 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 17:38:24 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:38:24 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:25 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 17:38:25 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 17:38:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 17:38:25 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:38:26 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:38:27 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:38:27 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 17:38:27 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.84秒
2025-07-19 17:38:27 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 17:38:27 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 17:38:28 - services.reddit_service - ERROR - 获取用户评论失败: received 403 HTTP response
2025-07-19 17:38:28 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:28 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 17:38:28 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:28 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:28 - services.reddit_service - ERROR - 获取用户帖子失败: received 403 HTTP response
2025-07-19 17:38:28 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 17:38:28 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 17:38:29 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:38:29 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 17:38:29 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 17:38:29 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:29 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 17:38:29 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 17:38:29 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:38:30 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 17:38:30 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 17:38:31 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:38:31 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 17:38:31 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 4.30秒
2025-07-19 17:38:31 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_173817_e2035bba.json
2025-07-19 17:38:31 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 14.29秒
2025-07-19 19:45:30 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 19:45:30 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:45:31 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 19:45:31 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 19:45:31 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 19:45:31 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:45:31 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:45:31 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 19:45:31 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:45:31 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:45:32 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 1.07秒
2025-07-19 19:45:32 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 1.10秒
2025-07-19 19:45:32 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 1.11秒
2025-07-19 19:45:32 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 19:45:32 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:45:32 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:32 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-19 19:45:33 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:45:33 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:33 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:45:33 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:33 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:45:33 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:33 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:45:33 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:36 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 19:45:36 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:45:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:36 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 19:45:36 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:45:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:36 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 19:45:36 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:45:36 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:37 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:45:37 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 19:45:37 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:45:37 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:38 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 19:45:38 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:45:38 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:45:38 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:45:39 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:45:40 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:45:41 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:45:41 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.73秒
2025-07-19 19:45:41 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 19:45:41 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 19:45:42 - services.reddit_service - ERROR - 获取用户评论失败: received 403 HTTP response
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:42 - services.reddit_service - ERROR - 获取用户帖子失败: received 403 HTTP response
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 19:45:42 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 19:45:42 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:43 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:43 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:43 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:45:43 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:45:44 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 19:45:44 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:44 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:44 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:44 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:45:44 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:44 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:44 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:44 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 19:45:44 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 19:45:44 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 19:45:45 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:45 - services.reddit_service - ERROR - 获取用户帖子失败: RedditPost.__init__() missing 2 required positional arguments: 'upvote_ratio' and 'permalink'
2025-07-19 19:45:45 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 19:45:45 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 4.44秒
2025-07-19 19:45:45 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_194531_e2035bba.json
2025-07-19 19:45:45 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 14.28秒
2025-07-19 19:53:02 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 19:53:02 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:53:02 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 19:53:02 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 19:53:02 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 19:53:02 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:53:02 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:53:02 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 19:53:02 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:53:02 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:53:03 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 0.91秒
2025-07-19 19:53:03 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.92秒
2025-07-19 19:53:03 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 0.92秒
2025-07-19 19:53:03 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 19:53:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:53:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:03 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-19 19:53:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:53:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:53:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:53:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:03 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:53:03 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:05 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 19:53:05 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:53:05 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:06 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:53:07 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 19:53:07 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:53:07 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:07 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 19:53:07 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:53:07 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:07 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 19:53:07 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:53:07 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:09 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:53:09 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 19:53:09 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:53:09 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:53:10 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:53:10 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:53:12 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:53:12 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.65秒
2025-07-19 19:53:12 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 19:53:12 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 19:53:13 - services.reddit_service - WARNING - 用户 Jean-Porte 的评论访问被禁止 (403): received 403 HTTP response
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:53:13 - services.reddit_service - WARNING - 用户 Jean-Porte 的帖子访问被禁止 (403): received 403 HTTP response
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的帖子: 1 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 19:53:13 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的帖子: 3 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的帖子: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的帖子: 5 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的帖子: 4 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的帖子: 7 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的帖子: 5 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的帖子: 1 条
2025-07-19 19:53:14 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 19:53:15 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:53:15 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的帖子: 5 条
2025-07-19 19:53:15 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:53:15 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:53:18 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的帖子: 7 条
2025-07-19 19:53:18 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:53:18 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 6.20秒
2025-07-19 19:53:18 - services.cogbridges_service - INFO - 搜索结果已保存: d:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_195302_e2035bba.json
2025-07-19 19:53:18 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 15.79秒
2025-07-19 19:53:18 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-19 19:54:40 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 19:54:40 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:54:40 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 19:54:40 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 19:54:40 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 19:54:40 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:54:40 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:54:40 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 19:54:40 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:54:40 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:54:41 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 0.74秒
2025-07-19 19:54:41 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 0.74秒
2025-07-19 19:54:41 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 0.74秒
2025-07-19 19:54:41 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 19:54:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:54:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:41 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-19 19:54:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:54:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:54:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:54:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:41 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:54:41 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:43 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 19:54:43 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:54:43 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:44 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 19:54:44 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:54:44 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:44 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 19:54:44 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:54:44 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:44 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:54:45 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 19:54:45 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:54:45 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:46 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:54:46 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 19:54:46 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:54:46 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:54:47 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:54:48 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:54:49 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:54:49 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 8.59秒
2025-07-19 19:54:49 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 19:54:49 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 19:54:50 - services.reddit_service - WARNING - 用户 Jean-Porte 的评论访问被禁止 (403): received 403 HTTP response
2025-07-19 19:54:50 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:50 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 19:54:50 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:50 - services.reddit_service - WARNING - 用户 Jean-Porte 的帖子访问被禁止 (403): received 403 HTTP response
2025-07-19 19:54:50 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的帖子: 1 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的帖子: 1 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 19:54:51 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的帖子: 3 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的帖子: 5 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的帖子: 1 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的帖子: 5 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的帖子: 7 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的帖子: 4 条
2025-07-19 19:54:52 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 19:54:53 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的帖子: 7 条
2025-07-19 19:54:53 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 19:54:54 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:55:07 - services.reddit_service - ERROR - 获取用户帖子失败: 
2025-07-19 19:55:07 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 17.63秒
2025-07-19 19:55:07 - services.cogbridges_service - INFO - 搜索结果已保存: D:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_195440_e2035bba.json
2025-07-19 19:55:07 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 26.96秒
2025-07-19 19:55:07 - services.cogbridges_service - INFO - CogBridges服务已关闭
2025-07-19 19:55:23 - services.google_search_api - INFO - Google搜索服务初始化成功（Custom Search API模式）
2025-07-19 19:55:23 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:55:23 - services.reddit_service - INFO - Reddit服务初始化成功
2025-07-19 19:55:23 - services.data_service - INFO - 数据存储服务初始化成功
2025-07-19 19:55:23 - services.cogbridges_service - INFO - CogBridges核心业务服务初始化成功
2025-07-19 19:55:23 - services.cogbridges_service - INFO - 开始CogBridges搜索流程: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:55:23 - services.cogbridges_service - INFO - 步骤1: Google搜索 - Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:55:23 - api.Google Custom Search - INFO - 开始调用 Google Custom Search API: GET search
2025-07-19 19:55:23 - services.google_search_api - INFO - 开始Google Custom Search API搜索: Should I subscribe to GPT, Claude, Grok, or Gemini?
2025-07-19 19:55:23 - utils.proxy_utils - INFO - 代理功能已禁用
2025-07-19 19:55:25 - services.google_search_api - INFO - Google搜索完成: 找到 5 个结果（总计 34600），耗时 2.26秒
2025-07-19 19:55:25 - api.Google Custom Search - INFO - Google Custom Search API调用成功，耗时: 2.26秒
2025-07-19 19:55:25 - services.cogbridges_service - INFO - 步骤1完成: 找到 5 个结果, 耗时: 2.27秒
2025-07-19 19:55:25 - services.cogbridges_service - INFO - 步骤2: 并行获取 5 个Reddit帖子的内容和评论
2025-07-19 19:55:25 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:55:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:25 - services.reddit_service - INFO - Reddit服务未使用代理
2025-07-19 19:55:25 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:55:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:25 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:55:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:25 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:55:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:25 - api.Reddit - INFO - 开始调用 Reddit API: GET submission
2025-07-19 19:55:25 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:27 - services.reddit_service - INFO - 获取帖子成功: How does Gemini, Grok or Llama compare to GPT or C...
2025-07-19 19:55:27 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:55:27 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:29 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:55:29 - services.reddit_service - INFO - 获取帖子成功: Would you say Grok 3 is better than GPT 4.5 or Cla...
2025-07-19 19:55:29 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:55:29 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:31 - services.reddit_service - INFO - 获取帖子成功: SVG Benchmark: Grok vs Gemini vs ChatGPT vs Claude...
2025-07-19 19:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:31 - services.reddit_service - INFO - 获取帖子成功: I tested Gemini 2.5 Pro against Claude 3.7 Sonnet ...
2025-07-19 19:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:31 - services.reddit_service - INFO - 获取帖子成功: Claude 3.5 Sonnet vs GPT-4: A programmer's perspec...
2025-07-19 19:55:31 - api.Reddit - INFO - 开始调用 Reddit API: GET comments
2025-07-19 19:55:31 - api.Reddit - INFO - Reddit API调用成功，耗时: 0.00秒
2025-07-19 19:55:32 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:55:33 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:55:33 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:55:34 - services.reddit_service - INFO - 获取评论成功: 6 条评论
2025-07-19 19:55:34 - services.cogbridges_service - INFO - 步骤2完成: 成功获取 5 个帖子数据, 耗时: 9.19秒
2025-07-19 19:55:34 - services.cogbridges_service - INFO - 步骤3: 并行获取评论者历史数据
2025-07-19 19:55:34 - services.cogbridges_service - INFO - 需要获取 28 个评论者的历史数据
2025-07-19 19:55:35 - services.reddit_service - WARNING - 用户 Jean-Porte 的评论访问被禁止 (403): received 403 HTTP response
2025-07-19 19:55:35 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的评论: 0 条
2025-07-19 19:55:35 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的评论: 20 条
2025-07-19 19:55:35 - services.reddit_service - INFO - 获取用户 MarinatedTechnician 在 ClaudeAI 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - WARNING - 用户 Jean-Porte 的帖子访问被禁止 (403): received 403 HTTP response
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Sad_Run_9798 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的评论: 1 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Gold_Bar_4072 在 singularity 的帖子: 1 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的评论: 1 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的评论: 20 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的评论: 20 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的评论: 20 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的评论: 2 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的评论: 4 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的评论: 15 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的评论: 7 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的评论: 0 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的评论: 5 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 HighPurrFormer 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:55:36 - services.reddit_service - INFO - 获取用户 adrian-dartagnan 在 grok 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 OfficeSalamander 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Chogo82 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 MrBietola 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的评论: 1 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的评论: 4 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Jester347 在 grok 的帖子: 5 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的评论: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Cagnazzo82 在 singularity 的帖子: 5 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 vasilenko93 在 singularity 的帖子: 4 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 xAragon_ 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 LeEasy 在 grok 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Excellent_Dealer3865 在 singularity 的帖子: 7 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 AutoModerator 在 grok 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 devonschmidt 在 ClaudeAI 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的评论: 4 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 freedomheaven 在 singularity 的帖子: 7 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的评论: 20 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 RamonDozol 在 grok 的帖子: 1 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Sure_Watercress_6053 在 grok 的帖子: 0 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 Its_not_a_tumor 在 singularity 的帖子: 5 条
2025-07-19 19:55:37 - services.reddit_service - INFO - 获取用户 focal-fossa 在 singularity 的帖子: 0 条
2025-07-19 19:55:38 - services.reddit_service - INFO - 获取用户 metaphorician 在 singularity 的帖子: 0 条
2025-07-19 19:55:38 - services.reddit_service - INFO - 获取用户 Axelwickm 在 singularity 的帖子: 0 条
2025-07-19 19:55:38 - services.reddit_service - INFO - 获取用户 Objective_Lab_3182 在 singularity 的帖子: 1 条
2025-07-19 19:55:39 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的评论: 2 条
2025-07-19 19:55:40 - services.reddit_service - INFO - 获取用户 Heavy_Hunt7860 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:55:43 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的评论: 3 条
2025-07-19 19:55:44 - services.reddit_service - INFO - 获取用户 Kathane37 在 ClaudeAI 的帖子: 3 条
2025-07-19 19:55:51 - services.reddit_service - ERROR - 获取用户评论失败: 
2025-07-19 19:55:52 - services.reddit_service - INFO - 获取用户 haslo 在 ClaudeAI 的帖子: 1 条
2025-07-19 19:55:52 - services.reddit_service - ERROR - 获取用户帖子失败: 
2025-07-19 19:55:52 - services.cogbridges_service - INFO - 步骤3完成: 获取了 28 个用户的历史数据, 耗时: 17.49秒
2025-07-19 19:55:52 - services.cogbridges_service - INFO - 搜索结果已保存: D:\pycharmproject\CogBridges_v020\data\results\cogbridges_search_20250719_195523_e2035bba.json
2025-07-19 19:55:52 - services.cogbridges_service - INFO - CogBridges搜索完成: Should I subscribe to GPT, Claude, Grok, or Gemini?, 总耗时: 28.96秒
2025-07-19 19:55:52 - services.cogbridges_service - INFO - CogBridges服务已关闭
