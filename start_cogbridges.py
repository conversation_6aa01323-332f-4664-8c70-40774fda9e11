#!/usr/bin/env python3
"""
CogBridges - 统一启动脚本
同时启动后端API服务器和前端Web界面的单一启动脚本
"""

import sys
import os
import subprocess
import threading
import time
import webbrowser
from pathlib import Path
import signal
import atexit

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config import config
from utils.logger_utils import get_logger


class CogBridgesLauncher:
    """CogBridges统一启动器"""
    
    def __init__(self):
        """初始化启动器"""
        self.logger = get_logger(__name__)
        self.backend_process = None
        self.frontend_server = None
        self.running = False
        
        # 注册退出处理
        atexit.register(self.cleanup)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查依赖...")
        
        required_packages = [
            'flask', 'flask_cors', 'requests', 'praw', 'asyncpraw',
            'google-api-python-client', 'tenacity', 'python-dotenv'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少以下依赖包:")
            for package in missing_packages:
                print(f"   - {package}")
            print(f"\n💡 请运行以下命令安装依赖:")
            print(f"   pip install -r requirements.txt")
            return False
        
        print("✅ 所有依赖已安装")
        return True
    
    def check_configuration(self):
        """检查配置"""
        print("🔧 检查配置...")
        
        # 检查必要的配置项
        required_configs = [
            ('GOOGLE_API_KEY', config.GOOGLE_API_KEY),
            ('GOOGLE_SEARCH_ENGINE_ID', config.GOOGLE_SEARCH_ENGINE_ID),
            ('REDDIT_CLIENT_ID', config.REDDIT_CLIENT_ID),
            ('REDDIT_CLIENT_SECRET', config.REDDIT_CLIENT_SECRET)
        ]
        
        missing_configs = []
        
        for name, value in required_configs:
            if not value or value == "your_api_key_here":
                missing_configs.append(name)
        
        if missing_configs:
            print(f"❌ 缺少以下配置:")
            for config_name in missing_configs:
                print(f"   - {config_name}")
            print(f"\n💡 请在config.py中配置这些参数")
            return False
        
        print("✅ 配置检查通过")
        return True
    
    def start_backend_api(self):
        """启动后端API服务器"""
        print("🚀 启动后端API服务器...")
        
        try:
            # 创建Flask应用
            from flask import Flask, jsonify, request
            from flask_cors import CORS
            from services.cogbridges_service import CogBridgesService
            
            app = Flask(__name__)
            CORS(app)
            
            # 初始化CogBridges服务
            cogbridges_service = CogBridgesService()
            
            @app.route('/api/health', methods=['GET'])
            def health_check():
                """健康检查接口"""
                return jsonify({
                    "status": "healthy",
                    "service": "CogBridges API",
                    "timestamp": time.time()
                })
            
            @app.route('/api/search', methods=['POST'])
            def search():
                """搜索接口"""
                try:
                    data = request.get_json()
                    query = data.get('query', '')
                    
                    if not query:
                        return jsonify({
                            "success": False,
                            "error": "查询参数不能为空"
                        }), 400
                    
                    # 执行搜索（同步调用异步函数）
                    import asyncio
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    try:
                        result = loop.run_until_complete(cogbridges_service.search(query))
                        
                        # 转换结果为JSON格式
                        response_data = {
                            "success": result.success,
                            "query": result.query,
                            "session_id": result.session_id,
                            "timestamp": result.timestamp.isoformat(),
                            "total_time": result.total_time,
                            "google_results": result.google_results,
                            "reddit_posts": result.reddit_posts,
                            "commenters_history": result.commenters_history,
                            "statistics": {
                                "google_search_time": result.google_search_time,
                                "reddit_posts_time": result.reddit_posts_time,
                                "commenters_history_time": result.commenters_history_time,
                                "google_results_count": len(result.google_results) if result.google_results else 0,
                                "reddit_posts_count": len(result.reddit_posts) if result.reddit_posts else 0,
                                "commenters_count": len(result.commenters_history) if result.commenters_history else 0
                            }
                        }
                        
                        if not result.success:
                            response_data["error"] = result.error_message
                        
                        return jsonify(response_data)
                        
                    finally:
                        loop.close()
                    
                except Exception as e:
                    self.logger.error(f"搜索请求处理失败: {e}")
                    return jsonify({
                        "success": False,
                        "error": f"服务器内部错误: {str(e)}"
                    }), 500
            
            # 在单独线程中启动Flask应用
            def run_flask():
                app.run(
                    host=config.HOST,
                    port=config.PORT,
                    debug=False,
                    threaded=True,
                    use_reloader=False
                )
            
            backend_thread = threading.Thread(target=run_flask, daemon=True)
            backend_thread.start()
            
            # 等待服务器启动
            time.sleep(2)
            
            # 测试API是否正常
            import requests
            try:
                response = requests.get(f"http://{config.HOST}:{config.PORT}/api/health", timeout=5)
                if response.status_code == 200:
                    print(f"✅ 后端API服务器启动成功: http://{config.HOST}:{config.PORT}")
                    return True
                else:
                    print(f"❌ 后端API服务器响应异常: {response.status_code}")
                    return False
            except requests.RequestException as e:
                print(f"❌ 后端API服务器连接失败: {e}")
                return False
                
        except Exception as e:
            self.logger.error(f"后端API服务器启动失败: {e}")
            print(f"❌ 后端API服务器启动失败: {e}")
            return False
    
    def start_frontend_server(self):
        """启动前端Web服务器"""
        print("🌐 启动前端Web服务器...")
        
        try:
            import http.server
            import socketserver
            from functools import partial
            
            # 设置前端文件目录
            web_dir = project_root / "web"
            if not web_dir.exists():
                print(f"❌ 前端文件目录不存在: {web_dir}")
                return False
            
            # 自定义HTTP请求处理器
            class CogBridgesHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, **kwargs):
                    super().__init__(*args, directory=str(web_dir), **kwargs)
                
                def end_headers(self):
                    # 添加CORS头
                    self.send_header('Access-Control-Allow-Origin', '*')
                    self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
                    self.send_header('Access-Control-Allow-Headers', 'Content-Type')
                    super().end_headers()
            
            # 启动HTTP服务器
            frontend_port = config.PORT + 1  # 前端端口为API端口+1
            
            def run_frontend_server():
                with socketserver.TCPServer(("", frontend_port), CogBridgesHTTPRequestHandler) as httpd:
                    self.frontend_server = httpd
                    print(f"✅ 前端Web服务器启动成功: http://localhost:{frontend_port}")
                    httpd.serve_forever()
            
            frontend_thread = threading.Thread(target=run_frontend_server, daemon=True)
            frontend_thread.start()
            
            time.sleep(1)
            return True
            
        except Exception as e:
            self.logger.error(f"前端Web服务器启动失败: {e}")
            print(f"❌ 前端Web服务器启动失败: {e}")
            return False
    
    def open_browser(self):
        """打开浏览器"""
        frontend_port = config.PORT + 1
        url = f"http://localhost:{frontend_port}"
        
        print(f"🌐 正在打开浏览器: {url}")
        
        try:
            webbrowser.open(url)
            print("✅ 浏览器已打开")
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"💡 请手动访问: {url}")
    
    def display_startup_info(self):
        """显示启动信息"""
        print("\n" + "=" * 60)
        print("🎉 CogBridges 启动成功！")
        print("=" * 60)
        print(f"🔗 后端API地址: http://{config.HOST}:{config.PORT}")
        print(f"🌐 前端Web地址: http://localhost:{config.PORT + 1}")
        print(f"📚 API文档: http://{config.HOST}:{config.PORT}/api/health")
        print("-" * 60)
        print("💡 使用说明:")
        print("  1. 在Web界面中输入搜索查询")
        print("  2. 系统将自动执行Google搜索 → Reddit数据获取 → 用户分析")
        print("  3. 查看完整的分析结果和用户画像")
        print("-" * 60)
        print("🛑 按 Ctrl+C 停止服务")
        print("=" * 60)
    
    def run(self):
        """运行启动器"""
        print("🌟 CogBridges 统一启动器")
        print("=" * 60)
        
        # 检查依赖和配置
        if not self.check_dependencies():
            return False
        
        if not self.check_configuration():
            return False
        
        # 启动后端API服务器
        if not self.start_backend_api():
            return False
        
        # 启动前端Web服务器
        if not self.start_frontend_server():
            return False
        
        # 显示启动信息
        self.display_startup_info()
        
        # 打开浏览器
        self.open_browser()
        
        # 保持运行状态
        self.running = True
        try:
            while self.running:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        
        return True
    
    def cleanup(self):
        """清理资源"""
        print("\n🧹 正在清理资源...")
        self.running = False
        
        if self.frontend_server:
            try:
                self.frontend_server.shutdown()
                print("✅ 前端服务器已停止")
            except:
                pass
        
        print("👋 CogBridges 已停止")
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在停止服务...")
        self.cleanup()
        sys.exit(0)


def main():
    """主函数"""
    launcher = CogBridgesLauncher()
    
    try:
        success = launcher.run()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n👋 用户中断，正在停止...")
        launcher.cleanup()
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
