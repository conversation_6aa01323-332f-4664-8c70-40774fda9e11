#!/usr/bin/env python3
"""
CogBridges 配置设置助手
帮助用户快速配置API密钥和环境变量
"""

import os
import sys
from pathlib import Path

def check_config():
    """检查当前配置状态"""
    print("🔍 检查当前配置状态...")
    print("=" * 50)
    
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env 文件不存在")
        return False
    
    # 读取.env文件
    config_status = {}
    
    with open(env_file, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                config_status[key] = value
    
    # 检查关键配置
    required_configs = {
        'GOOGLE_SEARCH_API_KEY': 'Google Custom Search API密钥',
        'GOOGLE_SEARCH_ENGINE_ID': 'Google搜索引擎ID',
        'REDDIT_CLIENT_ID': 'Reddit客户端ID',
        'REDDIT_CLIENT_SECRET': 'Reddit客户端密钥'
    }
    
    all_configured = True
    
    for key, description in required_configs.items():
        value = config_status.get(key, '')
        if not value or value == 'your_' + key.lower() + '_here':
            print(f"❌ {description}: 未配置")
            all_configured = False
        else:
            print(f"✅ {description}: 已配置")
    
    print("-" * 50)
    
    if all_configured:
        print("🎉 所有必要配置已完成！")
        return True
    else:
        print("⚠️ 部分配置缺失，请按照下面的指南进行配置")
        return False

def show_config_guide():
    """显示配置指南"""
    print("\n📚 API密钥获取指南")
    print("=" * 50)
    
    print("\n🔍 Google Custom Search API:")
    print("1. 访问 Google Cloud Console: https://console.cloud.google.com/")
    print("2. 创建新项目或选择现有项目")
    print("3. 启用 'Custom Search API'")
    print("4. 创建API密钥")
    print("5. 设置 Custom Search Engine: https://cse.google.com/cse/")
    print("   - 搜索整个网络")
    print("   - 获取搜索引擎ID")
    
    print("\n📱 Reddit API:")
    print("1. 访问 Reddit App Preferences: https://www.reddit.com/prefs/apps")
    print("2. 点击 'Create App' 或 'Create Another App'")
    print("3. 选择 'script' 类型")
    print("4. 填写应用信息")
    print("5. 获取 client_id 和 client_secret")
    
    print("\n💡 配置步骤:")
    print("1. 复制 .env.example 为 .env")
    print("2. 编辑 .env 文件，填入获取的API密钥")
    print("3. 运行 python setup_config.py 验证配置")

def interactive_setup():
    """交互式配置设置"""
    print("\n🛠️ 交互式配置设置")
    print("=" * 50)
    
    env_file = Path(".env")
    
    # 读取现有配置
    existing_config = {}
    if env_file.exists():
        with open(env_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        for line in content.split('\n'):
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                existing_config[key] = value
    else:
        # 复制模板文件
        template_file = Path(".env.example")
        if template_file.exists():
            with open(template_file, 'r', encoding='utf-8') as f:
                content = f.read()
        else:
            print("❌ .env.example 模板文件不存在")
            return False
    
    # 交互式输入
    configs_to_update = {
        'GOOGLE_SEARCH_API_KEY': 'Google Custom Search API密钥',
        'GOOGLE_SEARCH_ENGINE_ID': 'Google搜索引擎ID',
        'REDDIT_CLIENT_ID': 'Reddit客户端ID',
        'REDDIT_CLIENT_SECRET': 'Reddit客户端密钥',
        'REDDIT_USER_AGENT': 'Reddit用户代理 (可选)'
    }
    
    updated_config = existing_config.copy()
    
    for key, description in configs_to_update.items():
        current_value = existing_config.get(key, '')
        if current_value and not current_value.startswith('your_'):
            print(f"\n{description} 当前值: {current_value[:20]}...")
            update = input(f"是否更新? (y/n): ").lower().strip()
            if update not in ['y', 'yes', '是']:
                continue
        
        new_value = input(f"\n请输入 {description}: ").strip()
        if new_value:
            updated_config[key] = new_value
    
    # 更新.env文件
    try:
        # 重新构建.env文件内容
        new_content = []
        
        with open(Path(".env.example"), 'r', encoding='utf-8') as f:
            for line in f:
                line = line.rstrip()
                if '=' in line and not line.startswith('#'):
                    key = line.split('=')[0]
                    if key in updated_config:
                        new_content.append(f"{key}={updated_config[key]}")
                    else:
                        new_content.append(line)
                else:
                    new_content.append(line)
        
        with open(env_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(new_content))
        
        print(f"\n✅ 配置已保存到 {env_file}")
        return True
        
    except Exception as e:
        print(f"❌ 保存配置失败: {e}")
        return False

def test_apis():
    """测试API连接"""
    print("\n🧪 测试API连接...")
    print("=" * 50)
    
    try:
        # 导入配置
        from config import config
        
        # 测试Google API
        if config.google_search_configured:
            print("🔍 测试Google Custom Search API...")
            try:
                from services.google_search_api import GoogleSearchService
                google_service = GoogleSearchService()
                # 这里可以添加简单的API测试
                print("✅ Google API配置正确")
            except Exception as e:
                print(f"❌ Google API测试失败: {e}")
        else:
            print("⚠️ Google API未配置，跳过测试")
        
        # 测试Reddit API
        if config.reddit_configured:
            print("📱 测试Reddit API...")
            try:
                import praw
                reddit = praw.Reddit(
                    client_id=config.REDDIT_CLIENT_ID,
                    client_secret=config.REDDIT_CLIENT_SECRET,
                    user_agent=config.REDDIT_USER_AGENT
                )
                reddit.read_only = True
                # 简单测试
                print("✅ Reddit API配置正确")
            except Exception as e:
                print(f"❌ Reddit API测试失败: {e}")
        else:
            print("⚠️ Reddit API未配置，跳过测试")
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")

def main():
    """主函数"""
    print("🌟 CogBridges 配置设置助手")
    print("=" * 50)
    
    # 检查当前配置
    is_configured = check_config()
    
    if not is_configured:
        print("\n选择操作:")
        print("1. 查看配置指南")
        print("2. 交互式配置设置")
        print("3. 退出")
        
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == '1':
            show_config_guide()
        elif choice == '2':
            if interactive_setup():
                print("\n重新检查配置...")
                check_config()
                test_apis()
        elif choice == '3':
            print("👋 退出配置助手")
            return
        else:
            print("❌ 无效选择")
    else:
        # 测试API连接
        test_apis()
        print("\n🎉 配置完成！可以运行以下命令启动应用:")
        print("   python start_cogbridges.py")
        print("   python test_integration_complete.py")

if __name__ == "__main__":
    main()
