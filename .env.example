# CogBridges 环境配置模板
# 复制此文件为 .env 并填入实际的API密钥

# =============================================================================
# Google Custom Search API 配置
# =============================================================================
# 获取方式：https://developers.google.com/custom-search/v1/introduction
GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_search_engine_id_here

# =============================================================================
# Reddit API 配置
# =============================================================================
# 获取方式：https://www.reddit.com/prefs/apps
REDDIT_CLIENT_ID=your_reddit_client_id_here
REDDIT_CLIENT_SECRET=your_reddit_client_secret_here
REDDIT_USER_AGENT=CogBridges/1.0 by YourUsername

# =============================================================================
# 服务配置
# =============================================================================
HOST=127.0.0.1
PORT=5000
FLASK_DEBUG=True
FLASK_ENV=development

# =============================================================================
# 代理配置（可选）
# =============================================================================
# 如果需要使用代理访问API，请取消注释并配置
# HTTP_PROXY=http://127.0.0.1:7890
# HTTPS_PROXY=http://127.0.0.1:7890

# =============================================================================
# 数据存储配置
# =============================================================================
DATA_DIR=data
LOGS_DIR=data/logs
RESULTS_DIR=data/results

# =============================================================================
# 搜索配置
# =============================================================================
GOOGLE_SEARCH_RESULTS_COUNT=5
REDDIT_MAX_POSTS=10
REDDIT_MAX_COMMENTS_PER_POST=6
REDDIT_MAX_USER_COMMENTS=20
REDDIT_MAX_USER_POSTS=10

# =============================================================================
# 缓存配置
# =============================================================================
ENABLE_CACHE=True
CACHE_TTL_HOURS=24
