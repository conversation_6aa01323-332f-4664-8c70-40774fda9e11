#!/usr/bin/env python3
"""
简化的集成测试
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from services.cogbridges_service import CogBridgesService

async def simple_integration_test():
    """简化的集成测试"""
    print("🚀 简化集成测试开始")
    
    cogbridges = None
    try:
        # 初始化服务
        print("🔧 初始化CogBridges服务...")
        cogbridges = CogBridgesService()
        print("✅ 服务初始化成功")
        
        # 执行搜索
        print("🔍 执行搜索...")
        query = "Should I subscribe to GPT, <PERSON>, <PERSON><PERSON>, or <PERSON>?"
        result = await cogbridges.search(query)
        
        # 检查结果
        print(f"📊 搜索结果:")
        print(f"  成功: {result.success}")
        print(f"  总耗时: {result.total_time:.2f}秒")
        print(f"  Google结果: {len(result.google_results) if result.google_results else 0}")
        print(f"  Reddit帖子: {len(result.reddit_posts) if result.reddit_posts else 0}")
        print(f"  评论者历史: {len(result.commenters_history) if result.commenters_history else 0}")
        
        if result.success:
            print("✅ 集成测试成功")
            return True
        else:
            print(f"❌ 集成测试失败: {result.error_message}")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理资源
        if cogbridges:
            try:
                await cogbridges.close()
                print("🧹 资源清理完成")
            except Exception as e:
                print(f"⚠️ 资源清理失败: {e}")

if __name__ == "__main__":
    success = asyncio.run(simple_integration_test())
    sys.exit(0 if success else 1)
