# CogBridges Search - Python依赖包

# Web框架
Flask==2.3.3
Flask-CORS==4.0.0

# HTTP请求
requests==2.31.0
aiohttp==3.8.6
httpx==0.25.0

# Reddit API
praw==7.7.1
asyncpraw==7.7.1

# HTML解析
beautifulsoup4==4.12.2
lxml==4.9.3

# 异步支持
aiofiles==23.2.1

# 数据处理
pandas==2.1.1
numpy==1.25.2

# 配置管理
python-dotenv==1.0.0
pydantic==2.4.2

# 日志和监控
loguru==0.7.2
structlog==23.1.0

# 测试框架
pytest==7.4.2
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.11.1

# 代码质量
black==23.9.1
flake8==6.1.0
mypy==1.6.0

# 时间处理
python-dateutil==2.8.2

# JSON处理
orjson==3.9.7

# 代理支持
PySocks==1.7.1

# 开发工具
ipython==8.16.1
jupyter==1.0.0

# 性能监控
psutil==5.9.6

# 缓存
redis==5.0.1
diskcache==5.6.3

# 数据验证
marshmallow==3.20.1
cerberus==1.3.5

# 并发控制
tenacity==8.2.3
ratelimit==2.2.1

# 安全
cryptography==41.0.7
